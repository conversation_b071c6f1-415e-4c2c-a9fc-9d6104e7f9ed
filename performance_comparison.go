package main

import (
	"fmt"
	"time"
	"web_scanner/internal"
)

func main() {
	fmt.Println("=== goja vs Node.js 性能对比 ===")
	
	// 测试数据
	testJS := `
var config = {
    "api_key": "sk-1234567890abcdef",
    "database_url": "mysql://user:password@192.168.1.100:3306/db",
    "email": "<EMAIL>",
    "phone": "13812345678",
    "jwt_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c",
    "aws_access_key_id": "AKIAIOSFODNN7EXAMPLE",
    "sendgrid_api_key": "SG.1234567890abcdef.1234567890abcdef1234567890abcdef"
};
`
	
	// 测试goja方案
	fmt.Println("\n🔧 测试goja方案:")
	
	// 初始化时间
	start := time.Now()
	extractor := internal.NewOriginalFindSomethingExtractor()
	initTime := time.Since(start)
	
	if extractor == nil {
		fmt.Println("❌ goja初始化失败")
		return
	}
	
	fmt.Printf("✅ goja初始化耗时: %v\n", initTime)
	
	// 执行多次提取测试
	iterations := 100
	start = time.Now()
	
	for i := 0; i < iterations; i++ {
		result := extractor.ExtractSensitiveInfo(testJS, "https://test.com/app.js")
		if result == nil {
			fmt.Printf("❌ 第%d次提取失败\n", i+1)
			break
		}
	}
	
	totalTime := time.Since(start)
	avgTime := totalTime / time.Duration(iterations)
	
	fmt.Printf("✅ goja执行%d次提取总耗时: %v\n", iterations, totalTime)
	fmt.Printf("✅ goja平均每次提取耗时: %v\n", avgTime)
	
	// 计算理论Node.js性能
	fmt.Println("\n📊 理论性能对比:")
	nodeJSAvgTime := 200 * time.Millisecond // 保守估计每次200ms
	
	fmt.Printf("goja方案:    %v per extraction\n", avgTime)
	fmt.Printf("Node.js方案: %v per extraction (估计)\n", nodeJSAvgTime)
	
	speedup := float64(nodeJSAvgTime) / float64(avgTime)
	fmt.Printf("性能提升:    %.1fx 倍\n", speedup)
	
	// 内存使用对比
	fmt.Println("\n💾 资源使用对比:")
	fmt.Println("goja方案:")
	fmt.Println("  - 内存: 嵌入在Go程序中，共享内存")
	fmt.Println("  - 进程: 无额外进程")
	fmt.Println("  - 依赖: 无外部依赖")
	
	fmt.Println("Node.js方案:")
	fmt.Println("  - 内存: 每次启动新进程 ~30-50MB")
	fmt.Println("  - 进程: 每次提取启动新进程")
	fmt.Println("  - 依赖: 需要安装Node.js")
}
