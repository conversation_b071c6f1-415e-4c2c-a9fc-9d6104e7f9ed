package main

import (
	"encoding/json"
	"fmt"
	"log"
	"web_scanner/internal"
)

func main() {
	fmt.Println("=== 测试goja敏感信息提取功能 ===")
	
	// 创建敏感信息提取器
	extractor := internal.NewOriginalFindSomethingExtractor()
	if extractor == nil {
		log.Fatal("❌ 无法创建敏感信息提取器，可能是background.js文件不存在或有语法错误")
	}
	
	fmt.Println("✅ 成功创建敏感信息提取器")
	
	// 测试用的JavaScript代码，包含各种敏感信息
	testJS := `
var config = {
    "api_key": "sk-1234567890abcdef",
    "database_url": "mysql://user:password@***********00:3306/db",
    "email": "<EMAIL>",
    "phone": "13812345678",
    "jwt_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIxMjM0NTY3ODkwIiwibmFtZSI6IkpvaG4gRG9lIiwiaWF0IjoxNTE2MjM5MDIyfQ.SflKxwRJSMeKKF2QT4fwpMeJf36POk6yJV_adQssw5c",
    "api_endpoint": "/api/v1/users",
    "static_path": "/static/css/style.css",
    "domain": "api.example.com",
    "ip_address": "********",
    "ip_with_port": "***********:8080"
};

function encrypt(data) {
    return CryptoJS.AES.encrypt(data, "secret_key");
}

var paths = [
    "/admin/dashboard",
    "../config/database.json",
    "./assets/images/logo.png"
];
`
	
	// 执行敏感信息提取
	fmt.Println("\n=== 开始提取敏感信息 ===")
	result := extractor.ExtractSensitiveInfo(testJS, "https://test.example.com/app.js")
	
	if result == nil {
		log.Fatal("❌ 敏感信息提取失败，返回nil")
	}
	
	// 输出结果
	fmt.Printf("✅ 提取完成，状态: %s\n", result.Done)
	
	// 详细输出各类敏感信息
	fmt.Println("\n=== 提取结果详情 ===")
	
	if len(result.IP) > 0 {
		fmt.Printf("IP地址 (%d个): %v\n", len(result.IP), result.IP)
	}
	
	if len(result.IPPort) > 0 {
		fmt.Printf("IP:端口 (%d个): %v\n", len(result.IPPort), result.IPPort)
	}
	
	if len(result.Domain) > 0 {
		fmt.Printf("域名 (%d个): %v\n", len(result.Domain), result.Domain)
	}
	
	if len(result.Path) > 0 {
		fmt.Printf("路径 (%d个): %v\n", len(result.Path), result.Path)
	}
	
	if len(result.IncompletePath) > 0 {
		fmt.Printf("不完整路径 (%d个): %v\n", len(result.IncompletePath), result.IncompletePath)
	}
	
	if len(result.URL) > 0 {
		fmt.Printf("URL (%d个): %v\n", len(result.URL), result.URL)
	}
	
	if len(result.Mail) > 0 {
		fmt.Printf("邮箱 (%d个): %v\n", len(result.Mail), result.Mail)
	}
	
	if len(result.Mobile) > 0 {
		fmt.Printf("手机号 (%d个): %v\n", len(result.Mobile), result.Mobile)
	}
	
	if len(result.JWT) > 0 {
		fmt.Printf("JWT令牌 (%d个): %v\n", len(result.JWT), result.JWT)
	}
	
	if len(result.Algorithm) > 0 {
		fmt.Printf("算法 (%d个): %v\n", len(result.Algorithm), result.Algorithm)
	}
	
	if len(result.Secret) > 0 {
		fmt.Printf("密钥 (%d个): %v\n", len(result.Secret), result.Secret)
	}
	
	if len(result.Static) > 0 {
		fmt.Printf("静态文件 (%d个): %v\n", len(result.Static), result.Static)
	}
	
	// 输出完整的JSON结果用于调试
	fmt.Println("\n=== 完整JSON结果 ===")
	jsonBytes, err := json.MarshalIndent(result, "", "  ")
	if err != nil {
		log.Printf("❌ JSON序列化失败: %v", err)
	} else {
		fmt.Println(string(jsonBytes))
	}
}
