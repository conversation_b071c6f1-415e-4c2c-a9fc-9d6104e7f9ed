package main

import (
	"fmt"
	"os"
	"os/exec"
)

// 演示通过Node.js执行background.js的方案
func testNodeJSApproach() {
	fmt.Println("=== 测试通过Node.js执行background.js ===")
	
	// 1. 检查Node.js是否可用
	_, err := exec.LookPath("node")
	if err != nil {
		fmt.Printf("❌ Node.js未安装或不在PATH中: %v\n", err)
		return
	}
	fmt.Println("✅ Node.js可用")
	
	// 2. 创建适配的Node.js脚本
	nodeScript := `
// 模拟浏览器环境
global.chrome = {
	runtime: {
		onMessage: {
			addListener: function() {}
		}
	},
	tabs: {
		onUpdated: {
			addListener: function() {}
		},
		onActivated: {
			addListener: function() {}
		}
	},
	storage: {
		local: {
			get: function(keys, callback) {
				callback({});
			},
			set: function() {}
		}
	},
	action: {
		setBadgeText: function() {}
	}
};

global.Headers = function() {
	return {
		append: function() {}
	};
};

global.Request = function() {};
global.fetch = function() {
	return Promise.resolve({});
};

// 加载background.js
require('./assets/background.js');

// 测试提取功能
const testData = 'var config = {"api_key": "sk-test123", "email": "<EMAIL>"};';
try {
	const result = extract_info(testData);
	console.log(JSON.stringify(result, null, 2));
} catch (e) {
	console.error('提取失败:', e.message);
}
`
	
	// 3. 写入临时脚本文件
	scriptPath := "temp_node_script.js"
	err = os.WriteFile(scriptPath, []byte(nodeScript), 0644)
	if err != nil {
		fmt.Printf("❌ 写入脚本失败: %v\n", err)
		return
	}
	defer os.Remove(scriptPath)
	
	// 4. 执行Node.js脚本
	cmd := exec.Command("node", scriptPath)
	output, err := cmd.CombinedOutput()
	if err != nil {
		fmt.Printf("❌ Node.js执行失败: %v\n", err)
		fmt.Printf("错误输出: %s\n", string(output))
		return
	}
	
	fmt.Printf("✅ Node.js执行结果:\n%s\n", string(output))
}

func main() {
	testNodeJSApproach()
}
