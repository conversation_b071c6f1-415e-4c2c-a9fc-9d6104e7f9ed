package internal

import (
	"encoding/json"
	"os"
	"path/filepath"
	"strings"

	"github.com/dop251/goja"
)

// OriginalFindSomethingExtractor 基于goja引擎的敏感信息提取器
// 使用原版findsomething的JavaScript代码进行敏感信息提取
type OriginalFindSomethingExtractor struct {
	vm *goja.Runtime // goja JavaScript运行时
}

// NewOriginalFindSomethingExtractor 创建新的敏感信息提取器实例
// 初始化goja运行时并加载JavaScript提取代码
// 如果无法读取或执行JavaScript文件，将返回nil
//
// 返回值：
//   - *OriginalFindSomethingExtractor: 敏感信息提取器实例，失败时返回nil
func NewOriginalFindSomethingExtractor() *OriginalFindSomethingExtractor {
	vm := goja.New()

	// 尝试读取JavaScript提取代码
	var jsCode []byte
	var err error

	// 尝试从assets目录读取background.js
	jsPath := filepath.Join("assets", "background.js")
	jsCode, err = os.ReadFile(jsPath)
	if err != nil {
		// 如果读取失败，返回nil（必须依赖外部JavaScript文件）
		return nil
	}

	// 创建适配的JavaScript环境
	adaptedCode := adaptJSForGoja(string(jsCode))

	// 在goja中执行JavaScript代码
	_, err = vm.RunString(adaptedCode)
	if err != nil {
		// 如果执行失败，返回nil（必须依赖外部JavaScript文件）
		return nil
	}

	return &OriginalFindSomethingExtractor{
		vm: vm,
	}
}

// ExtractSensitiveInfo 从给定的内容中提取敏感信息
// 使用JavaScript引擎执行提取逻辑，完全兼容findsomething的提取规则
//
// 参数：
//   - content: 要分析的内容（HTML或JavaScript代码）
//   - sourceURL: 内容来源URL
//
// 返回值：
//   - *FindSomethingResult: 提取到的敏感信息结果
func (e *OriginalFindSomethingExtractor) ExtractSensitiveInfo(content, sourceURL string) *FindSomethingResult {
	if e.vm == nil {
		return nil
	}
	
	// 设置全局变量
	e.vm.Set("inputData", content)
	e.vm.Set("sourceURL", sourceURL)
	
	// 执行提取函数
	result, err := e.vm.RunString(`
		(function() {
			try {
				// 使用extract_info函数提取敏感信息
				var extractedData = extract_info(inputData);
				
				// 构建符合FindSomethingResult格式的结果
				var result = {
					current: sourceURL,
					done: "completed",
					tasklist: [],
					donetasklist: [],
					pretasknum: 1,
					source: {},
					ip: extractedData.ip || [],
					ip_port: extractedData.ip_port || [],
					domain: extractedData.domain || [],
					path: extractedData.path || [],
					incomplete_path: extractedData.incomplete_path || [],
					url: extractedData.url || [],
					sfz: extractedData.sfz || [],
					mobile: extractedData.mobile || [],
					mail: extractedData.mail || [],
					jwt: extractedData.jwt || [],
					algorithm: extractedData.algorithm || [],
					secret: extractedData.secret || [],
					static: extractedData.static || []
				};
				
				// 设置source映射
				result.source[sourceURL] = sourceURL;
				
				return result;
			} catch (e) {
				// 如果提取失败，返回空结果
				return {
					current: sourceURL,
					done: "error",
					tasklist: [],
					donetasklist: [],
					pretasknum: 0,
					source: {},
					ip: [],
					ip_port: [],
					domain: [],
					path: [],
					incomplete_path: [],
					url: [],
					sfz: [],
					mobile: [],
					mail: [],
					jwt: [],
					algorithm: [],
					secret: [],
					static: []
				};
			}
		})()
	`)
	
	if err != nil {
		return nil
	}
	
	// 将JavaScript结果转换为Go结构体
	resultObj := result.Export()
	jsonBytes, err := json.Marshal(resultObj)
	if err != nil {
		return nil
	}
	
	var findSomethingResult FindSomethingResult
	err = json.Unmarshal(jsonBytes, &findSomethingResult)
	if err != nil {
		return nil
	}
	
	return &findSomethingResult
}

// adaptJSForGoja 适配JavaScript代码以在goja环境中运行
// 移除浏览器特定的API调用，保留核心提取逻辑
func adaptJSForGoja(jsCode string) string {
	// 移除Chrome扩展相关的代码
	adapted := strings.ReplaceAll(jsCode, "chrome.runtime.onMessage.addListener", "// chrome.runtime.onMessage.addListener")
	adapted = strings.ReplaceAll(adapted, "chrome.tabs.onUpdated.addListener", "// chrome.tabs.onUpdated.addListener")
	adapted = strings.ReplaceAll(adapted, "chrome.tabs.onActivated.addListener", "// chrome.tabs.onActivated.addListener")
	adapted = strings.ReplaceAll(adapted, "chrome.storage.local.set", "// chrome.storage.local.set")
	
	// 移除fetch相关的异步代码
	adapted = strings.ReplaceAll(adapted, "fetch(", "// fetch(")
	adapted = strings.ReplaceAll(adapted, "Promise.all", "// Promise.all")
	
	// 移除console.log（可选）
	adapted = strings.ReplaceAll(adapted, "console.log", "// console.log")
	
	return adapted
}